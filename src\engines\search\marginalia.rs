use reqwest::Url;
use serde::Deserialize;

use crate::{
    engines::{EngineResponse, CLIENT},
    parse::{parse_html_response_with_opts, ParseOpts},
};

#[derive(Deserialize)]
pub struct MarginaliaConfig {
    pub args: MarginaliaArgs,
}
#[derive(Deserialize)]
pub struct MarginaliaArgs {
    pub profile: String,
    pub js: String,
    pub adtech: String,
}

pub fn request(query: &str) -> reqwest::RequestBuilder {
    // if the query is more than 3 words or has any special characters then abort
    if query.split_whitespace().count() > 3
        || !query.chars().all(|c| c.is_ascii_alphanumeric() || c == ' ')
    {
        // Return a dummy request that will be ignored
        return CLIENT.get("https://example.com");
    }

    // Use default values for the config since we can't access the config from just the query string
    CLIENT
        .get(
            Url::parse_with_params(
                "https://search.marginalia.nu/search",
                &[
                    ("query", query),
                    ("profile", "default"),
                    ("js", "false"),
                    ("adtech", "false"),
                ],
            )
            .unwrap(),
        )
}

pub fn parse_response(body: &str) -> eyre::Result<EngineResponse> {
    parse_html_response_with_opts(
        body,
        ParseOpts::new()
            .result("section.search-result")
            .title("h2")
            .href("a[href]")
            .description("p.description"),
    )
}
