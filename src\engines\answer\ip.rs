use maud::html;

use crate::engines::EngineResponse;

use super::regex;

pub fn request(query: &str) -> EngineResponse {
    // This function needs the IP address, but we can't get it from just the query string
    // For now, we'll return an empty response
    if !regex!("^what('s|s| is) my ip").is_match(&query.to_lowercase()) {
        return EngineResponse::new();
    }

    // We can't access the IP address from just the query string
    let ip = "IP address not available";

    EngineResponse::answer_html(html! {
        h3 { b { (ip) } }
    })
}
