use maud::html;

use crate::engines::EngineResponse;

use super::regex;

pub fn request(query: &str) -> EngineResponse {
    if !regex!("^(what('s|s| is) my (user ?agent|ua)|ua|user ?agent)$")
        .is_match(&query.to_lowercase())
    {
        return EngineResponse::new();
    }

    // We can't access the request headers from just the query string
    let user_agent: Option<&str> = None;

    // Create a simplified version without headers
    let all_headers_html = html! {
        br;
        details {
            summary { "All headers" }
            div {
                "Headers not available in this context"
            }
        }
    };

    EngineResponse::answer_html(if let Some(user_agent) = user_agent {
        html! {
            h3 { b { (user_agent) } }
            (all_headers_html)
        }
    } else {
        html! {
            "You don't have a user agent"
            (all_headers_html)
        }
    })
}
