use eyre::eyre;
use scraper::{Html, Selector};
use url::Url;

use crate::{
    engines::{EngineNewsResult, EngineNewsResponse, RequestResponse, CLIENT},
};

pub fn request(query: &str) -> RequestResponse {
    CLIENT
        .get(
            Url::parse_with_params(
                "https://www.bing.com/news/search",
                &[
                    ("q", query),
                    ("form", "TNSA"),
                ],
            )
            .unwrap(),
        )
        .into()
}

pub fn parse_response(body: &str) -> eyre::Result<EngineNewsResponse> {
    let dom = Html::parse_document(body);
    let mut news_results = Vec::new();

    // Select news items
    let news_selector = Selector::parse("div.news-card").unwrap();
    
    for news_el in dom.select(&news_selector) {
        // Extract title and URL
        let title_selector = Selector::parse("a.title").unwrap();
        
        if let Some(title_el) = news_el.select(&title_selector).next() {
            let title = title_el.text().collect::<String>().trim().to_string();
            
            let news_url = title_el
                .value()
                .attr("href")
                .map(|href| {
                    if href.starts_with("http") {
                        href.to_string()
                    } else {
                        format!("https://www.bing.com{}", href)
                    }
                })
                .unwrap_or_default();
            
            // Extract source/publisher
            let source_selector = Selector::parse("div.source").unwrap();
            let source = news_el
                .select(&source_selector)
                .next()
                .map(|el| el.text().collect::<String>().trim().to_string());
            
            // Extract published date
            let date_selector = Selector::parse("span.time").unwrap();
            let published_date = news_el
                .select(&date_selector)
                .next()
                .map(|el| el.text().collect::<String>().trim().to_string());
            
            // Extract snippet/description
            let snippet_selector = Selector::parse("div.snippet").unwrap();
            let snippet = news_el
                .select(&snippet_selector)
                .next()
                .map(|el| el.text().collect::<String>().trim().to_string());
            
            // Extract thumbnail if available
            let thumbnail_selector = Selector::parse("div.image img").unwrap();
            let thumbnail_url = news_el
                .select(&thumbnail_selector)
                .next()
                .and_then(|el| el.value().attr("src"))
                .map(|src| {
                    if src.starts_with("//") {
                        format!("https:{}", src)
                    } else if src.starts_with("/") {
                        format!("https://www.bing.com{}", src)
                    } else {
                        src.to_string()
                    }
                });
            
            news_results.push(EngineNewsResult {
                news_url,
                title,
                source,
                published_date,
                snippet,
                thumbnail_url,
            });
        }
    }

    Ok(EngineNewsResponse { news_results })
}
