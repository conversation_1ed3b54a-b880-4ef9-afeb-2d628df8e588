use url::Url;
use scraper::{Html, Selector};

use crate::{
    engines::{EngineResponse, EngineVideoResult, EngineVideosResponse, RequestResponse, CLIENT},
    parse::{parse_html_response_with_opts, ParseOpts, QueryMethod},
};

pub fn request(query: &str) -> RequestResponse {
    CLIENT
        .get(
            Url::parse_with_params("https://html.duckduckgo.com/html", &[("q", query)]).unwrap()
        )
        .into()
}

pub fn parse_response(body: &str) -> eyre::Result<EngineResponse> {
    use scraper::{ElementRef, Selector};

    parse_html_response_with_opts(
        body,
        ParseOpts::new()
            .result(".result")
            .title(".result__title")
            .href(QueryMethod::Manual(Box::new(|el: &ElementRef| {
                let url = el
                    .select(&Selector::parse(".result__url").unwrap())
                    .next()
                    .map(|n| n.text().collect::<String>())
                    .unwrap_or_default();

                // Clean up the URL by removing whitespace and newlines
                let url = url.trim().replace('\n', "").replace('\r', "");

                // Add proper protocol if missing
                let url = if url.starts_with("//") {
                    format!("https:{}", url)
                } else if url.starts_with("/") {
                    format!("https://duckduckgo.com{}", url)
                } else if !url.is_empty() && !url.contains("://") {
                    // Add https:// to URLs that don't have a protocol
                    format!("https://{}", url)
                } else {
                    url
                };

                Ok(url)
            })))
            .description(".result__snippet"),
    )
}

pub fn request_videos(query: &str) -> RequestResponse {
    CLIENT
        .get(
            Url::parse_with_params(
                "https://duckduckgo.com/",
                &[
                    ("q", query),
                    ("ia", "videos"),
                    ("iax", "videos"),
                ],
            )
            .unwrap(),
        )
        .into()
}

pub fn parse_videos_response(body: &str) -> eyre::Result<EngineVideosResponse> {
    let dom = Html::parse_document(body);
    let mut video_results = Vec::new();

    // Extract video tiles
    let video_selector = Selector::parse("div.tile--vid").unwrap();

    for video_el in dom.select(&video_selector) {
        // Extract video URL
        let anchor_selector = Selector::parse("a.tile--vid__link").unwrap();
        if let Some(anchor) = video_el.select(&anchor_selector).next() {
            if let Some(href) = anchor.value().attr("href") {
                let video_url = if href.starts_with("http") {
                    href.to_string()
                } else {
                    format!("https://duckduckgo.com{}", href)
                };

                // Extract title
                let title_selector = Selector::parse("h6.tile__title").unwrap();
                let title = video_el
                    .select(&title_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string())
                    .unwrap_or_else(|| "Untitled Video".to_string());

                // Extract thumbnail
                let thumbnail_selector = Selector::parse("img.tile__media__img").unwrap();
                let thumbnail_url = video_el
                    .select(&thumbnail_selector)
                    .next()
                    .and_then(|el| el.value().attr("src"))
                    .map(|src| {
                        if src.starts_with("//") {
                            format!("https:{}", src)
                        } else if src.starts_with("/") {
                            format!("https://duckduckgo.com{}", src)
                        } else {
                            src.to_string()
                        }
                    })
                    .unwrap_or_default();

                // Extract duration
                let duration_selector = Selector::parse("span.tile__duration").unwrap();
                let duration = video_el
                    .select(&duration_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string());

                // Extract source/publisher
                let source_selector = Selector::parse("span.tile__source").unwrap();
                let published = video_el
                    .select(&source_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string());

                video_results.push(EngineVideoResult {
                    video_url,
                    thumbnail_url,
                    title,
                    duration,
                    views: None,
                    published,
                });
            }
        }
    }

    Ok(EngineVideosResponse { video_results })
}
