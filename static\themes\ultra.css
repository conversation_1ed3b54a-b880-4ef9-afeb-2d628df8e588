/* Audio search styles */
.audio-results {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.audio-result {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 1rem;
    transition: background-color 0.2s;
}

.audio-result:hover {
    background-color: #e9e9e9;
}

.audio-result-anchor {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
}

.audio-result-thumbnail-container {
    position: relative;
    min-width: 120px;
    height: 120px;
    background-color: #ddd;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-result-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.audio-result-thumbnail-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
}

.audio-icon {
    font-size: 2.5rem;
}

.audio-result-duration {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 0.8rem;
}

.audio-result-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.audio-result-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #1a0dab;
}

.audio-result-metadata {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.audio-result-artist {
    font-weight: 500;
}

/* Document search styles */
.document-results {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.document-result {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 1rem;
    transition: background-color 0.2s;
}

.document-result:hover {
    background-color: #e9e9e9;
}

.document-result-anchor {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
}

.document-result-thumbnail-container {
    position: relative;
    min-width: 80px;
    height: 100px;
    background-color: #ddd;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.document-result-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.document-result-thumbnail-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
}

.document-icon {
    font-size: 2.5rem;
}

.document-result-size {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 0.8rem;
}

.document-result-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.document-result-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #1a0dab;
}

.document-result-metadata {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.document-result-type {
    font-weight: 500;
    background-color: #e0e0e0;
    padding: 2px 6px;
    border-radius: 3px;
}

.document-result-snippet {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

/* Pagination styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    gap: 1rem;
}

.pagination-link {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #f0f0f0;
    color: #1a0dab;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.pagination-link:hover {
    background-color: #e0e0e0;
}

.pagination-current {
    color: #666;
    font-size: 0.9rem;
}
