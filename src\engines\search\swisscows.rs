//! Swisscows search engine implementation

use url::Url;
use scraper::{Html, Selector, ElementRef};
use crate::engines::{EngineResponse, EngineSearchResult, RequestResponse, CLIENT};
use crate::parse::{parse_html_response_with_opts, ParseOpts, QueryMethod};

pub fn request(query: &str) -> RequestResponse {
    RequestResponse::Http(
        CLIENT.get(
            Url::parse_with_params("https://swisscows.com/web", &[("query", query)]).unwrap()
        )
    )
}

pub fn parse_response(body: &str) -> eyre::Result<EngineResponse> {
    parse_html_response_with_opts(
        body,
        ParseOpts::new()
            .result("div.swisscows-result")
            .title("a.swisscows-result__title")
            .href(QueryMethod::Manual(Box::new(|el: &ElementRef| {
                let url = el
                    .select(&Selector::parse("a.swisscows-result__title").unwrap())
                    .next()
                    .and_then(|n| n.value().attr("href"))
                    .unwrap_or_default()
                    .to_string();
                Ok(url)
            })))
            .description("div.swisscows-result__description"),
    )
}