#[macro_export]
macro_rules! engines {
    ($($engine:ident = $id:expr),* $(,)?) => {
        #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, PartialOrd, Ord, Serialize)]
        pub enum Engine {
            $($engine,)*
        }

        impl Engine {
            #[must_use]
            pub fn all() -> &'static [Engine] {
                &[$(Engine::$engine,)*]
            }

            #[must_use]
            pub fn id(&self) -> &'static str {
                match self {
                    $(Engine::$engine => $id,)*
                }
            }
        }

        impl FromStr for Engine {
            type Err = ();

            fn from_str(s: &str) -> Result<Self, Self::Err> {
                match s {
                    $($id => Ok(Engine::$engine),)*
                    _ => Err(()),
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_parse_response {
    ($res:ident, $module:ident::$engine_id:ident::None) => {
        None
    };
    ($res:ident, $module:ident::$engine_id:ident::$parse_response:ident) => {
        Some($module::$engine_id::$parse_response($res.into()))
    };
}

#[macro_export]
macro_rules! engine_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn request(&self, query: &SearchQuery) -> RequestResponse {
                #[allow(clippy::useless_conversion)]
                match self {
                    $(
                        Engine::$engine => {
                            // Handle different function signatures
                            // This is a workaround for the type system
                            // Some functions expect &SearchQuery, others expect &str
                            let result = std::panic::catch_unwind(|| {
                                // First try with &SearchQuery
                                $module::$engine_id::$request(query).into()
                            });

                            if result.is_ok() {
                                result.unwrap()
                            } else {
                                // If that fails, try with &str
                                $module::$engine_id::$request(&query.query).into()
                            }
                        },
                    )*
                    _ => RequestResponse::None,
                }
            }

            #[tracing::instrument(skip(self, res), fields(engine = %self))]
            pub fn parse_response(&self, res: &HttpResponse) -> eyre::Result<EngineResponse> {
                #[allow(clippy::useless_conversion)]
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { res, $module::$engine_id::$parse_response }
                            .ok_or_else(|| eyre::eyre!("engine {self:?} can't parse response"))?,
                    )*
                    _ => eyre::bail!("engine {self:?} can't parse response"),
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_autocomplete_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn request_autocomplete(&self, query: &str) -> Option<RequestAutocompleteResponse> {
                match self {
                    $(
                        Engine::$engine => Some($module::$engine_id::$request(query).into()),
                    )*
                    _ => None,
                }
            }

            pub fn parse_autocomplete_response(&self, body: &str) -> eyre::Result<Vec<String>> {
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { body, $module::$engine_id::$parse_response }
                            .ok_or_else(|| eyre::eyre!("engine {self:?} can't parse autocomplete response"))?,
                    )*
                    _ => eyre::bail!("engine {self:?} can't parse autocomplete response"),
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_postsearch_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn postsearch_request(&self, response: &Response) -> Option<reqwest::RequestBuilder> {
                match self {
                    $(
                        Engine::$engine => $module::$engine_id::$request(response),
                    )*
                    _ => None,
                }
            }

            #[must_use]
            pub fn postsearch_parse_response(&self, res: &HttpResponse) -> Option<maud::PreEscaped<String>> {
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { res, $module::$engine_id::$parse_response }?,
                    )*
                    _ => None,
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_image_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn request_images(&self, query: &SearchQuery) -> RequestResponse {
                match self {
                    $(
                        Engine::$engine => $module::$engine_id::$request(query).into(),
                    )*
                    _ => RequestResponse::None,
                }
            }

            pub fn parse_images_response(&self, res: &HttpResponse) -> eyre::Result<EngineImagesResponse> {
                #[allow(clippy::useless_conversion)]
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { res, $module::$engine_id::$parse_response }
                            .ok_or_else(|| eyre::eyre!("engine {self:?} can't parse images response"))?,
                    )*
                    _ => eyre::bail!("engine {self:?} can't parse response"),
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_video_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn request_videos(&self, query: &SearchQuery) -> RequestResponse {
                match self {
                    $(
                        Engine::$engine => $module::$engine_id::$request(query).into(),
                    )*
                    _ => RequestResponse::None,
                }
            }

            pub fn parse_videos_response(&self, res: &HttpResponse) -> eyre::Result<EngineVideosResponse> {
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { res, $module::$engine_id::$parse_response }
                            .ok_or_else(|| eyre::eyre!("engine {self:?} can't parse videos response"))?,
                    )*
                    _ => eyre::bail!("engine {self:?} can't parse videos response"),
                }
            }
        }
    };
}

#[macro_export]
macro_rules! engine_news_requests {
    ($($engine:ident => $module:ident::$engine_id:ident::$request:ident, $parse_response:ident),* $(,)?) => {
        impl Engine {
            #[must_use]
            pub fn request_news(&self, query: &SearchQuery) -> RequestResponse {
                match self {
                    $(
                        Engine::$engine => $module::$engine_id::$request(query).into(),
                    )*
                    _ => RequestResponse::None,
                }
            }

            pub fn parse_news_response(&self, res: &HttpResponse) -> eyre::Result<EngineNewsResponse> {
                match self {
                    $(
                        Engine::$engine => $crate::engine_parse_response! { res, $module::$engine_id::$parse_response }
                            .ok_or_else(|| eyre::eyre!("engine {self:?} can't parse news response"))?,
                    )*
                    _ => eyre::bail!("engine {self:?} can't parse news response"),
                }
            }
        }
    };
}
