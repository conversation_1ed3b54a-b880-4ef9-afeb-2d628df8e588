use std::{
    collections::{BTreeSet, HashMap},
    fmt::{self, Display},
    net::IpAddr,
    ops::Deref,
    str::FromStr,
    sync::{Arc, LazyLock},
    time::{Duration, Instant},
};

use eyre::bail;
use futures::future::join_all;
use maud::PreEscaped;
use reqwest::{header::HeaderMap, RequestBuilder};
use serde::{Deserialize, Deserializer, Serialize};
use tokio::sync::mpsc;
use tracing::{error, info};

mod macros;
mod ranking;
use crate::{
    config::Config, engine_autocomplete_requests, engine_image_requests, engine_news_requests,
    engine_postsearch_requests, engine_requests, engines, engine_video_requests,
};

pub mod answer;
pub mod postsearch;
pub mod search;

engines! {
    // search
    Google = "google",
    GoogleScholar = "google_scholar",
    Bing = "bing",
    <PERSON> = "brave",
    Marginalia = "marginalia",
    RightDao = "rightdao",
    Stract = "stract",
    Yep = "yep",
    DuckDuckGo = "duckduckgo",
    Mojeek = "mojeek",
    Qwant = "qwant",
    Ecosia = "ecosia",
    Startpage = "startpage",
    Metager = "metager",
    Swisscows = "swisscows",
    // video search
    YouTube = "youtube",
    Vimeo = "vimeo",
    // news search
    GoogleNews = "google_news",
    BingNews = "bing_news",
    // answer
    Dictionary = "dictionary",
    Fend = "fend",
    Ip = "ip",
    Notepad = "notepad",
    ColorPicker = "colorpicker",
    Numbat = "numbat",
    Thesaurus = "thesaurus",
    Timezone = "timezone",
    Useragent = "useragent",
    Wikipedia = "wikipedia",
    // post-search
    DocsRs = "docs_rs",
    GitHub = "github",
    Mdn = "mdn",
    MinecraftWiki = "minecraft_wiki",
    StackExchange = "stackexchange",
}

engine_requests! {
    // search
    Bing => search::bing::request, parse_response,
    Brave => search::brave::request, parse_response,
    GoogleScholar => search::google_scholar::request, parse_response,
    Google => search::google::request, parse_response,
    Marginalia => search::marginalia::request, parse_response,
    RightDao => search::rightdao::request, parse_response,
    Stract => search::stract::request, parse_response,
    Yep => search::yep::request, parse_response,
    DuckDuckGo => search::duckduckgo::request, parse_response,
    Mojeek => search::mojeek::request, parse_response,
    Qwant => search::qwant::request, parse_response,
    Ecosia => search::ecosia::request, parse_response,
    Startpage => search::startpage::request, parse_response,
    Metager => search::metager::request, parse_response,
    Swisscows => search::swisscows::request, parse_response,
    // answer
    Dictionary => answer::dictionary::request, parse_response,
    Fend => answer::fend::request, None,
    Ip => answer::ip::request, None,
    Notepad => answer::notepad::request, None,
    ColorPicker => answer::colorpicker::request, None,
    Numbat => answer::numbat::request, None,
    Thesaurus => answer::thesaurus::request, parse_response,
    Timezone => answer::timezone::request, None,
    Useragent => answer::useragent::request, None,
    Wikipedia => answer::wikipedia::request, parse_response,
}

engine_autocomplete_requests! {
    Google => search::google::request_autocomplete, parse_autocomplete_response,
    Fend => answer::fend::request_autocomplete, None,
    Numbat => answer::numbat::request_autocomplete, None,
}

engine_postsearch_requests! {
    DocsRs => postsearch::docs_rs::request, parse_response,
    GitHub => postsearch::github::request, parse_response,
    Mdn => postsearch::mdn::request, parse_response,
    MinecraftWiki => postsearch::minecraft_wiki::request, parse_response,
    StackExchange => postsearch::stackexchange::request, parse_response,
}

engine_image_requests! {
    Google => search::google::request_images, parse_images_response,
    Bing => search::bing::request_images, parse_images_response,
}

engine_video_requests! {
    Google => search::google::request_videos, parse_videos_response,
    Bing => search::bing::request_videos, parse_videos_response,
    DuckDuckGo => search::duckduckgo::request_videos, parse_videos_response,
    YouTube => search::youtube::request, parse_response,
    Vimeo => search::vimeo::request, parse_response,
}

engine_news_requests! {
    GoogleNews => search::google_news::request, parse_response,
    BingNews => search::bing_news::request, parse_response,
}

impl fmt::Display for Engine {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.id())
    }
}

impl<'de> Deserialize<'de> for Engine {
    fn deserialize<D>(deserializer: D) -> Result<Engine, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Engine::from_str(&s).map_err(|_| serde::de::Error::custom(format!("invalid engine '{s}'")))
    }
}

#[derive(Clone)]
pub struct SearchQuery {
    pub query: String,
    pub tab: SearchTab,
    pub request_headers: HashMap<String, String>,
    pub ip: String,
    /// The page number for pagination (1-based)
    pub page: usize,
    /// The config is part of the query so it's possible to make a query with a
    /// custom config.
    pub config: Arc<Config>,
}

// Trait to handle different request function signatures
pub trait RequestHandler {
    fn handle_request(&self, query: &SearchQuery) -> RequestResponse;
}

// Implementation for functions that take &str
pub struct StrRequestHandler<F>(pub F)
where
    F: Fn(&str) -> reqwest::RequestBuilder;

impl<F> RequestHandler for StrRequestHandler<F>
where
    F: Fn(&str) -> reqwest::RequestBuilder,
{
    fn handle_request(&self, query: &SearchQuery) -> RequestResponse {
        RequestResponse::Http((self.0)(&query.query))
    }
}

// Implementation for functions that take &SearchQuery
pub struct QueryRequestHandler<F>(pub F)
where
    F: Fn(&SearchQuery) -> EngineResponse;

impl<F> RequestHandler for QueryRequestHandler<F>
where
    F: Fn(&SearchQuery) -> EngineResponse,
{
    fn handle_request(&self, query: &SearchQuery) -> RequestResponse {
        RequestResponse::Instant((self.0)(query))
    }
}

impl Deref for SearchQuery {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        &self.query
    }
}

#[derive(Default, Debug, Clone, Copy, PartialEq, Eq)]
pub enum SearchTab {
    #[default]
    All,
    Images,
    Videos,
    News,
    Audio,
    Documents,
}

impl FromStr for SearchTab {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "all" => Ok(Self::All),
            "images" => Ok(Self::Images),
            "videos" => Ok(Self::Videos),
            "news" => Ok(Self::News),
            "audio" => Ok(Self::Audio),
            "documents" => Ok(Self::Documents),
            _ => Err(()),
        }
    }
}

impl Display for SearchTab {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::All => write!(f, "all"),
            Self::Images => write!(f, "images"),
            Self::Videos => write!(f, "videos"),
            Self::News => write!(f, "news"),
            Self::Audio => write!(f, "audio"),
            Self::Documents => write!(f, "documents"),
        }
    }
}

pub enum RequestResponse {
    None,
    Http(reqwest::RequestBuilder),
    Instant(EngineResponse),
}
impl From<reqwest::RequestBuilder> for RequestResponse {
    fn from(req: reqwest::RequestBuilder) -> Self {
        Self::Http(req)
    }
}
impl From<EngineResponse> for RequestResponse {
    fn from(res: EngineResponse) -> Self {
        Self::Instant(res)
    }
}

pub enum RequestAutocompleteResponse {
    Http(reqwest::RequestBuilder),
    Instant(Vec<String>),
}
impl From<reqwest::RequestBuilder> for RequestAutocompleteResponse {
    fn from(req: reqwest::RequestBuilder) -> Self {
        Self::Http(req)
    }
}
impl From<Vec<String>> for RequestAutocompleteResponse {
    fn from(res: Vec<String>) -> Self {
        Self::Instant(res)
    }
}

pub struct HttpResponse {
    pub res: reqwest::Response,
    pub body: String,
    pub config: Arc<Config>,
}

impl<'a> From<&'a HttpResponse> for &'a str {
    fn from(res: &'a HttpResponse) -> Self {
        &res.body
    }
}

impl From<HttpResponse> for reqwest::Response {
    fn from(res: HttpResponse) -> Self {
        res.res
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineSearchResult {
    pub url: String,
    pub title: String,
    pub description: String,
}

#[derive(Debug)]
pub struct EngineFeaturedSnippet {
    pub url: String,
    pub title: String,
    pub description: String,
}

#[derive(Debug, Default)]
pub struct EngineResponse {
    pub search_results: Vec<EngineSearchResult>,
    pub featured_snippet: Option<EngineFeaturedSnippet>,
    pub answer_html: Option<PreEscaped<String>>,
    pub infobox_html: Option<PreEscaped<String>>,
}

#[derive(Default)]
pub struct EngineImagesResponse {
    pub image_results: Vec<EngineImageResult>,
}

#[derive(Debug, Default)]
pub struct EngineVideosResponse {
    pub video_results: Vec<EngineVideoResult>,
}

impl EngineResponse {
    #[must_use]
    pub fn new() -> Self {
        Self::default()
    }

    #[must_use]
    pub fn answer_html(html: PreEscaped<String>) -> Self {
        Self {
            answer_html: Some(html),
            ..Default::default()
        }
    }

    #[must_use]
    pub fn infobox_html(html: PreEscaped<String>) -> Self {
        Self {
            infobox_html: Some(html),
            ..Default::default()
        }
    }
}

impl EngineImagesResponse {
    #[must_use]
    pub fn new() -> Self {
        Self::default()
    }
}

impl EngineVideosResponse {
    #[must_use]
    pub fn new() -> Self {
        Self::default()
    }
}

impl EngineNewsResponse {
    #[must_use]
    pub fn new() -> Self {
        Self::default()
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineImageResult {
    pub image_url: String,
    pub page_url: String,
    pub title: String,
    pub width: u64,
    pub height: u64,
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineVideoResult {
    pub video_url: String,
    pub thumbnail_url: String,
    pub title: String,
    pub duration: Option<String>,
    pub views: Option<String>,
    pub published: Option<String>,
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineNewsResult {
    pub news_url: String,
    pub title: String,
    pub source: Option<String>,
    pub published_date: Option<String>,
    pub snippet: Option<String>,
    pub thumbnail_url: Option<String>,
}

#[derive(Debug, Default)]
pub struct EngineNewsResponse {
    pub news_results: Vec<EngineNewsResult>,
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineAudioResult {
    pub audio_url: String,
    pub title: String,
    pub thumbnail_url: Option<String>,
    pub duration: Option<String>,
    pub artist: Option<String>,
    pub published: Option<String>,
}

#[derive(Debug, Default)]
pub struct EngineAudioResponse {
    pub audio_results: Vec<EngineAudioResult>,
}

#[derive(Debug, Clone, Serialize)]
pub struct EngineDocumentResult {
    pub document_url: String,
    pub title: String,
    pub thumbnail_url: Option<String>,
    pub file_type: Option<String>,
    pub file_size: Option<String>,
    pub published: Option<String>,
    pub snippet: Option<String>,
}

#[derive(Debug, Default)]
pub struct EngineDocumentsResponse {
    pub document_results: Vec<EngineDocumentResult>,
}

#[derive(Debug)]
pub enum EngineProgressUpdate {
    Requesting,
    Downloading,
    Parsing,
    Done,
    Error(String),
}

#[derive(Debug)]
pub enum ProgressUpdateData {
    Engine {
        engine: Engine,
        update: EngineProgressUpdate,
    },
    Response(ResponseForTab),
    PostSearchInfobox(Infobox),
}

#[derive(Debug)]
pub struct ProgressUpdate {
    pub data: ProgressUpdateData,
    pub time_ms: u64,
}

impl ProgressUpdate {
    #[must_use]
    pub fn new(data: ProgressUpdateData, start_time: Instant) -> Self {
        Self {
            data,
            time_ms: start_time.elapsed().as_millis() as u64,
        }
    }
}

async fn make_request(
    request: RequestBuilder,
    engine: Engine,
    query: &SearchQuery,
    send_engine_progress_update: impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<HttpResponse> {
    send_engine_progress_update(engine, EngineProgressUpdate::Requesting);

    let mut res = request.send().await?;

    send_engine_progress_update(engine, EngineProgressUpdate::Downloading);

    let mut body_bytes = Vec::new();
    while let Some(chunk) = res.chunk().await? {
        body_bytes.extend_from_slice(&chunk);
    }
    let body = String::from_utf8_lossy(&body_bytes).to_string();

    send_engine_progress_update(engine, EngineProgressUpdate::Parsing);

    let http_response = HttpResponse {
        res,
        body,
        config: query.config.clone(),
    };
    Ok(http_response)
}

async fn make_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    let mut requests = Vec::new();
    for &engine in Engine::all() {
        let engine_config = query.config.engines.get(engine);
        if !engine_config.enabled {
            continue;
        }

        requests.push(async move {
            // Special handling for Google to support pagination
            let request_response = if engine == Engine::Google {
                // Use the pagination-aware request function
                RequestResponse::Http(search::google::request_with_pagination(&query.query, query.page))
            } else {
                engine.request(query)
            };

            let response = match request_response {
                RequestResponse::Http(request) => {
                    let http_response =
                        match make_request(request, engine, query, send_engine_progress_update)
                            .await
                        {
                            Ok(http_response) => http_response,
                            Err(e) => {
                                send_engine_progress_update(
                                    engine,
                                    EngineProgressUpdate::Error(e.to_string()),
                                );
                                return Err(e);
                            }
                        };

                    let response = match engine.parse_response(&http_response) {
                        Ok(response) => response,
                        Err(e) => {
                            error!("parse error for {engine}: {e}");
                            send_engine_progress_update(
                                engine,
                                EngineProgressUpdate::Error(e.to_string()),
                            );
                            return Err(e);
                        }
                    };

                    send_engine_progress_update(engine, EngineProgressUpdate::Done);

                    response
                }
                RequestResponse::Instant(response) => response,
                RequestResponse::None => EngineResponse::new(),
            };

            Ok((engine, response))
        });
    }

    let mut response_futures = Vec::new();
    for request in requests {
        response_futures.push(request);
    }

    let mut responses = HashMap::new();
    for response_result in join_all(response_futures).await {
        let response_result: eyre::Result<_> = response_result; // this line is necessary to make type inference work
        if let Ok((engine, response)) = response_result {
            responses.insert(engine, response);
        }
    }

    let response = ranking::merge_engine_responses(query.config.clone(), responses);
    let has_infobox = response.infobox.is_some();
    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::All(response.clone())),
        start_time,
    ))?;

    if !has_infobox {
        // post-search

        let mut postsearch_requests = Vec::new();
        for &engine in Engine::all() {
            let engine_config = query.config.engines.get(engine);
            if !engine_config.enabled {
                continue;
            }

            if let Some(request) = engine.postsearch_request(&response) {
                postsearch_requests.push(async move {
                    let response = match request.send().await {
                        Ok(mut res) => {
                            let mut body_bytes = Vec::new();
                            while let Some(chunk) = res.chunk().await? {
                                body_bytes.extend_from_slice(&chunk);
                            }
                            let body = String::from_utf8_lossy(&body_bytes).to_string();

                            let http_response = HttpResponse {
                                res,
                                body,
                                config: query.config.clone(),
                            };
                            engine.postsearch_parse_response(&http_response)
                        }
                        Err(e) => {
                            error!("postsearch request error: {e}");
                            None
                        }
                    };
                    Ok((engine, response))
                });
            }
        }

        let mut postsearch_response_futures = Vec::new();
        for request in postsearch_requests {
            postsearch_response_futures.push(request);
        }

        let postsearch_responses_result: eyre::Result<HashMap<_, _>> =
            join_all(postsearch_response_futures)
                .await
                .into_iter()
                .collect();
        let postsearch_responses = postsearch_responses_result?;

        for (engine, response) in postsearch_responses {
            if let Some(html) = response {
                progress_tx.send(ProgressUpdate::new(
                    ProgressUpdateData::PostSearchInfobox(Infobox { html, engine }),
                    start_time,
                ))?;
                // break so we don't send multiple infoboxes
                break;
            }
        }
    }

    Ok(())
}

async fn make_image_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    let mut requests = Vec::new();
    for &engine in Engine::all() {
        let engine_config = query.config.engines.get(engine);
        if !engine_config.enabled {
            continue;
        }

        requests.push(async move {
            let request_response = engine.request_images(query);

            let response = match request_response {
                RequestResponse::Http(request) => {
                    let http_response =
                        make_request(request, engine, query, send_engine_progress_update).await?;

                    let response = match engine.parse_images_response(&http_response) {
                        Ok(response) => response,
                        Err(e) => {
                            error!("parse error for {engine} (images): {e}");
                            EngineImagesResponse::new()
                        }
                    };

                    send_engine_progress_update(engine, EngineProgressUpdate::Done);

                    response
                }
                RequestResponse::Instant(_) => {
                    error!("unexpected instant response for image request");
                    EngineImagesResponse::new()
                }
                RequestResponse::None => EngineImagesResponse::new(),
            };

            Ok((engine, response))
        });
    }

    let mut response_futures = Vec::new();
    for request in requests {
        response_futures.push(request);
    }

    let responses_result: eyre::Result<HashMap<_, _>> =
        join_all(response_futures).await.into_iter().collect();
    let responses = responses_result?;

    let response = ranking::merge_images_responses(query.config.clone(), responses);
    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::Images(response.clone())),
        start_time,
    ))?;

    Ok(())
}

async fn make_video_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    let mut requests = Vec::new();
    for &engine in Engine::all() {
        let engine_config = query.config.engines.get(engine);
        if !engine_config.enabled {
            continue;
        }

        requests.push(async move {
            let request_response = engine.request_videos(query);

            let response = match request_response {
                RequestResponse::Http(request) => {
                    let http_response =
                        make_request(request, engine, query, send_engine_progress_update).await?;

                    let response = match engine.parse_videos_response(&http_response) {
                        Ok(response) => response,
                        Err(e) => {
                            error!("parse error for {engine} (videos): {e}");
                            EngineVideosResponse::new()
                        }
                    };

                    send_engine_progress_update(engine, EngineProgressUpdate::Done);

                    response
                }
                RequestResponse::Instant(_) => {
                    error!("unexpected instant response for video request");
                    EngineVideosResponse::new()
                }
                RequestResponse::None => EngineVideosResponse::new(),
            };

            Ok((engine, response))
        });
    }

    let mut response_futures = Vec::new();
    for request in requests {
        response_futures.push(request);
    }

    let responses_result: eyre::Result<HashMap<_, _>> =
        join_all(response_futures).await.into_iter().collect();
    let responses = responses_result?;

    let response = ranking::merge_videos_responses(query.config.clone(), responses);
    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::Videos(response.clone())),
        start_time,
    ))?;

    Ok(())
}

async fn make_news_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    let mut requests = Vec::new();
    for &engine in Engine::all() {
        let engine_config = query.config.engines.get(engine);
        if !engine_config.enabled {
            continue;
        }

        requests.push(async move {
            let request_response = engine.request_news(query);

            let response = match request_response {
                RequestResponse::Http(request) => {
                    let http_response =
                        make_request(request, engine, query, send_engine_progress_update).await?;

                    let response = match engine.parse_news_response(&http_response) {
                        Ok(response) => response,
                        Err(e) => {
                            error!("parse error for {engine} (news): {e}");
                            EngineNewsResponse::new()
                        }
                    };

                    send_engine_progress_update(engine, EngineProgressUpdate::Done);

                    response
                }
                RequestResponse::Instant(_) => {
                    error!("unexpected instant response for news request");
                    EngineNewsResponse::new()
                }
                RequestResponse::None => EngineNewsResponse::new(),
            };

            Ok((engine, response))
        });
    }

    let mut response_futures = Vec::new();
    for request in requests {
        response_futures.push(request);
    }

    let responses_result: eyre::Result<HashMap<_, _>> =
        join_all(response_futures).await.into_iter().collect();
    let responses = responses_result?;

    let response = ranking::merge_news_responses(query.config.clone(), responses);
    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::News(response.clone())),
        start_time,
    ))?;

    Ok(())
}

async fn make_audio_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    _send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    // For now, we'll just return an empty response since we haven't implemented audio search engines yet
    let audio_results = Vec::new();
    let response = AudioResponse {
        audio_results,
        config: query.config.clone(),
    };

    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::Audio(response)),
        start_time,
    ))?;

    Ok(())
}

async fn make_document_requests(
    query: &SearchQuery,
    progress_tx: &mpsc::UnboundedSender<ProgressUpdate>,
    start_time: Instant,
    _send_engine_progress_update: &impl Fn(Engine, EngineProgressUpdate),
) -> eyre::Result<()> {
    // For now, we'll just return an empty response since we haven't implemented document search engines yet
    let document_results = Vec::new();
    let response = DocumentsResponse {
        document_results,
        config: query.config.clone(),
    };

    progress_tx.send(ProgressUpdate::new(
        ProgressUpdateData::Response(ResponseForTab::Documents(response)),
        start_time,
    ))?;

    Ok(())
}

#[tracing::instrument(fields(query = %query.query), skip(progress_tx))]
pub async fn search(
    query: &SearchQuery,
    progress_tx: mpsc::UnboundedSender<ProgressUpdate>,
) -> eyre::Result<()> {
    let start_time = Instant::now();

    info!("Doing search");

    let progress_tx = &progress_tx;
    let send_engine_progress_update = |engine: Engine, update: EngineProgressUpdate| {
        let _ = progress_tx.send(ProgressUpdate::new(
            ProgressUpdateData::Engine { engine, update },
            start_time,
        ));
    };

    match query.tab {
        SearchTab::All => {
            make_requests(query, progress_tx, start_time, &send_engine_progress_update).await?
        }
        SearchTab::Images if query.config.image_search.enabled => {
            make_image_requests(query, progress_tx, start_time, &send_engine_progress_update)
                .await?
        }
        SearchTab::Videos if query.config.video_search.enabled => {
            make_video_requests(query, progress_tx, start_time, &send_engine_progress_update).await?
        }
        SearchTab::News if query.config.news_search.enabled => {
            make_news_requests(query, progress_tx, start_time, &send_engine_progress_update).await?
        }
        SearchTab::Audio if query.config.audio_search.enabled => {
            make_audio_requests(query, progress_tx, start_time, &send_engine_progress_update).await?
        }
        SearchTab::Documents if query.config.documents_search.enabled => {
            make_document_requests(query, progress_tx, start_time, &send_engine_progress_update).await?
        }
        _ => {
            bail!("unknown tab or feature not enabled");
        }
    }

    Ok(())
}

pub async fn autocomplete(config: &Config, query: &str) -> eyre::Result<Vec<String>> {
    let mut requests = Vec::new();
    for &engine in Engine::all() {
        let config = config.engines.get(engine);
        if !config.enabled {
            continue;
        }

        if let Some(request) = engine.request_autocomplete(query) {
            requests.push(async move {
                let response = match request {
                    RequestAutocompleteResponse::Http(request) => {
                        let res = request.send().await?;
                        let body = res.text().await?;
                        engine.parse_autocomplete_response(&body)?
                    }
                    RequestAutocompleteResponse::Instant(response) => response,
                };
                Ok((engine, response))
            });
        }
    }

    let mut autocomplete_futures = Vec::new();
    for request in requests {
        autocomplete_futures.push(request);
    }

    let autocomplete_results_result: eyre::Result<HashMap<_, _>> =
        join_all(autocomplete_futures).await.into_iter().collect();
    let autocomplete_results = autocomplete_results_result?;

    Ok(ranking::merge_autocomplete_responses(
        config,
        autocomplete_results,
    ))
}

pub static CLIENT: LazyLock<reqwest::Client> = LazyLock::new(|| {
    reqwest::ClientBuilder::new()
        .local_address(IpAddr::from_str("0.0.0.0").unwrap())
        // we pretend to be a normal browser so websites don't block us
        // (since we're not entirely a bot, we're acting on behalf of the user)
        .user_agent("Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0")
        .default_headers({
            let mut headers = HeaderMap::new();
            headers.insert("Accept-Language", "en-US,en;q=0.5".parse().unwrap());
            headers
        })
        .timeout(Duration::from_secs(10))
        .build()
        .unwrap()
});

#[derive(Debug, Clone, Serialize)]
pub struct Response {
    pub search_results: Vec<SearchResult<EngineSearchResult>>,
    pub featured_snippet: Option<FeaturedSnippet>,
    pub answer: Option<Answer>,
    pub infobox: Option<Infobox>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
pub struct ImagesResponse {
    pub image_results: Vec<SearchResult<EngineImageResult>>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
pub struct VideosResponse {
    pub video_results: Vec<SearchResult<EngineVideoResult>>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
pub struct NewsResponse {
    pub news_results: Vec<SearchResult<EngineNewsResult>>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
pub struct AudioResponse {
    pub audio_results: Vec<SearchResult<EngineAudioResult>>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
pub struct DocumentsResponse {
    pub document_results: Vec<SearchResult<EngineDocumentResult>>,
    #[serde(skip)]
    pub config: Arc<Config>,
}

#[derive(Debug, Clone, Serialize)]
#[serde(untagged)]
pub enum ResponseForTab {
    All(Response),
    Images(ImagesResponse),
    Videos(VideosResponse),
    News(NewsResponse),
    Audio(AudioResponse),
    Documents(DocumentsResponse),
}

#[derive(Debug, Clone, Serialize)]
pub struct SearchResult<R: Serialize> {
    pub result: R,
    pub engines: BTreeSet<Engine>,
    pub score: f64,
}

#[derive(Debug, Clone, Serialize)]
pub struct FeaturedSnippet {
    pub url: String,
    pub title: String,
    pub description: String,
    pub engine: Engine,
}

#[derive(Debug, Clone, Serialize)]
pub struct Answer {
    #[serde(serialize_with = "serialize_markup")]
    pub html: PreEscaped<String>,
    pub engine: Engine,
}

#[derive(Debug, Clone, Serialize)]
pub struct Infobox {
    #[serde(serialize_with = "serialize_markup")]
    pub html: PreEscaped<String>,
    pub engine: Engine,
}

pub struct AutocompleteResult {
    pub query: String,
    pub score: f64,
}

fn serialize_markup<S>(markup: &PreEscaped<String>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    serializer.serialize_str(&markup.0)
}
