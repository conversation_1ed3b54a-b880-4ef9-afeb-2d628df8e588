use eyre::eyre;
use scraper::{Html, Selector};
use url::Url;

use crate::{
    engines::{EngineNewsResult, EngineNewsResponse, RequestResponse, CLIENT},
};

pub fn request(query: &str) -> RequestResponse {
    CLIENT
        .get(
            Url::parse_with_params(
                "https://www.google.com/search",
                &[
                    ("q", query),
                    ("tbm", "nws"),  // Switch to news search
                    ("nfpr", "1"),   // No autocorrect
                ],
            )
            .unwrap(),
        )
        .into()
}

pub fn parse_response(body: &str) -> eyre::Result<EngineNewsResponse> {
    let dom = Html::parse_document(body);
    let mut news_results = Vec::new();

    // Select news items
    let news_selector = Selector::parse("div.SoaBEf").unwrap();
    
    for news_el in dom.select(&news_selector) {
        // Extract title and URL
        let title_selector = Selector::parse("div.n0jPhd").unwrap();
        let anchor_selector = Selector::parse("a").unwrap();
        
        let title = news_el
            .select(&title_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string())
            .unwrap_or_else(|| "Untitled News".to_string());
        
        let news_url = news_el
            .select(&anchor_selector)
            .next()
            .and_then(|el| el.value().attr("href"))
            .map(|href| {
                if href.starts_with("/url?") {
                    // Extract the actual URL from Google's redirect
                    if let Some(q_param) = href.split('&').find(|p| p.starts_with("q=")) {
                        urlencoding::decode(&q_param[2..])
                            .unwrap_or_default()
                            .to_string()
                    } else {
                        format!("https://www.google.com{}", href)
                    }
                } else if href.starts_with("http") {
                    href.to_string()
                } else {
                    format!("https://www.google.com{}", href)
                }
            })
            .unwrap_or_default();
        
        // Extract source/publisher
        let source_selector = Selector::parse("span.xQ82C").unwrap();
        let source = news_el
            .select(&source_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string());
        
        // Extract published date
        let date_selector = Selector::parse("span.OSrXXb").unwrap();
        let published_date = news_el
            .select(&date_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string());
        
        // Extract snippet/description
        let snippet_selector = Selector::parse("div.GI74Re").unwrap();
        let snippet = news_el
            .select(&snippet_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string());
        
        // Extract thumbnail if available
        let thumbnail_selector = Selector::parse("img.tvs3Id").unwrap();
        let thumbnail_url = news_el
            .select(&thumbnail_selector)
            .next()
            .and_then(|el| el.value().attr("src"))
            .map(ToString::to_string);
        
        news_results.push(EngineNewsResult {
            news_url,
            title,
            source,
            published_date,
            snippet,
            thumbnail_url,
        });
    }

    Ok(EngineNewsResponse { news_results })
}
