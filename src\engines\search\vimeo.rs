use reqwest::Url;
use scraper::{Html, Selector};
use eyre::eyre;

use crate::{
    engines::{EngineVideoResult, EngineVideosResponse, RequestResponse, CLIENT},
};

pub fn request(query: &str) -> RequestResponse {
    CLIENT
        .get(
            Url::parse_with_params(
                "https://vimeo.com/search",
                &[("q", query)],
            )
            .unwrap(),
        )
        .into()
}

pub fn parse_response(body: &str) -> eyre::Result<EngineVideosResponse> {
    let dom = Html::parse_document(body);
    let mut video_results = Vec::new();

    // Select video items
    let video_selector = Selector::parse("div[data-search-result-type='video']").unwrap();
    
    for video_el in dom.select(&video_selector) {
        // Extract video URL and title
        let anchor_selector = Selector::parse("a.iris_link-box").unwrap();
        if let Some(anchor) = video_el.select(&anchor_selector).next() {
            if let Some(href) = anchor.value().attr("href") {
                let video_url = if href.starts_with("https://") {
                    href.to_string()
                } else {
                    format!("https://vimeo.com{}", href)
                };
                
                // Extract title
                let title_selector = Selector::parse("p.iris_title").unwrap();
                let title = video_el
                    .select(&title_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string())
                    .unwrap_or_else(|| "Untitled Vimeo Video".to_string());
                
                // Extract thumbnail
                let thumbnail_selector = Selector::parse("img.iris_video-vital__image").unwrap();
                let thumbnail_url = video_el
                    .select(&thumbnail_selector)
                    .next()
                    .and_then(|el| el.value().attr("src"))
                    .map(ToString::to_string)
                    .unwrap_or_default();
                
                // Extract duration
                let duration_selector = Selector::parse("span.iris_video-vital__duration").unwrap();
                let duration = video_el
                    .select(&duration_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string());
                
                // Extract metadata (views, publish date)
                let meta_selector = Selector::parse("div.iris_video-vital__meta").unwrap();
                let meta_text = video_el
                    .select(&meta_selector)
                    .next()
                    .map(|el| el.text().collect::<String>().trim().to_string())
                    .unwrap_or_default();
                
                // Parse views and published date from meta text
                // Format is typically: "X views · Y ago"
                let mut views = None;
                let mut published = None;
                
                if let Some(parts_idx) = meta_text.find(" · ") {
                    views = Some(meta_text[0..parts_idx].trim().to_string());
                    published = Some(meta_text[parts_idx + 3..].trim().to_string());
                }
                
                video_results.push(EngineVideoResult {
                    video_url,
                    thumbnail_url,
                    title,
                    duration,
                    views,
                    published,
                });
            }
        }
    }

    Ok(EngineVideosResponse { video_results })
}
